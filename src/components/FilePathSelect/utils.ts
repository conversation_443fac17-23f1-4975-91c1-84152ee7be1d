import {CatalogType, EntityType, EnumMetaType} from '@api/metaRequest';
import * as http from '@api/metaRequest';
import {Privilege} from '@api/permission/type';
import {MetaCnNameMap} from '@components/MetaCreateModal/constants';
import {RULE} from '@utils/regs';
import {PathLevel} from '.';
import {isBuildInCatalog} from '@pages/MetaData/helper';

export const metaContent = {
  volume: {
    title: 'Volume',
    pathName: 'Volumes',
    invalidPathTip: '路径必须类似于 /Volumes/<catalog>/<schema>/<volume>/<path>',
    listFunc: (workspaceId: string, params: any) => {
      return http.getVolumeList(workspaceId, {...params}, Privilege.WriteVolume);
    },
    folderListFunc: (workspaceId: string, params: any) => {
      const {catalogName, schemaName, volumeName, path} = params;
      const reqPath = `/Volumes/${path}`;
      const fullName = `${catalogName}.${schemaName}.${volumeName}`;
      return http.getVolumeFileList(workspaceId, fullName, {
        path: reqPath
      });
    }
  },
  dataset: {
    title: 'Dataset',
    pathName: 'Datasets',
    invalidPathTip: '路径必须类似于 /Datasets/<catalog>/<schema>/<dataset>/<version>/<path>',
    listFunc: (workspaceId: string, params: any) => {
      return http.getDatasetOrModelList(workspaceId, http.EnumMetaType.DATASETS, {...params});
    },
    folderListFunc: (workspaceId: string, params: any) => {
      const {catalogName, schemaName, volumeName, path, versionName} = params;
      const reqPath = `/Datasets/${path}`;
      const fullName = `${catalogName}.${schemaName}.${volumeName}`;
      return http.getDatasetOrModelVersionFileList(
        workspaceId,
        EnumMetaType.DATASETS,
        fullName,
        versionName,
        {
          path: reqPath
        }
      );
    }
  },
  model: {
    title: 'Model',
    pathName: 'Models',
    invalidPathTip: '路径必须类似于 /Models/<catalog>/<schema>/<model>/<version>/<path>',
    listFunc: (workspaceId: string, params: any) => {
      return http.getDatasetOrModelList(workspaceId, http.EnumMetaType.MODELS, {...params});
    },
    folderListFunc: (workspaceId: string, params: any) => {
      const {catalogName, schemaName, volumeName, path, versionName} = params;
      const reqPath = `/Models/${path}`;
      const fullName = `${catalogName}.${schemaName}.${volumeName}`;
      return http.getDatasetOrModelVersionFileList(workspaceId, EnumMetaType.MODELS, fullName, versionName, {
        path: reqPath
      });
    }
  },
  table: {
    title: 'Table',
    pathName: 'Tables',
    invalidPathTip: '路径必须类似于 <catalog>.<schema>.<table>',
    listFunc: (workspaceId: string, params: any) => {
      return http.getTableList(workspaceId, params);
    }
  }
};

export const getCatalogList = async (workspaceId: string, hasDoris) => {
  // 根据类型排过序 后续开发需注意
  const res = await http.getCatalogList(workspaceId);
  const catalogList =
    res.result.catalogs
      .filter((item) => (hasDoris ? item : item.type !== CatalogType.DORIS))
      .map((item) => item.name)
      .filter((item) => !isBuildInCatalog(item as CatalogType)) || []; // 过滤掉内置的 catalog
  return {list: catalogList || []};
};

export const getSchemaList = async (workspaceId: string, catalogName: string) => {
  const schemaRes = await http.getSchemaList(workspaceId, {catalogName});
  const hasAdd = await getCatalogDetail(workspaceId, catalogName);
  const schemaList = schemaRes.result.schemas;
  return {list: schemaList || [], hasAdd};
};

export const getVolumeList = async (
  workspaceId: string,
  catalogName: string,
  schemaName: string,
  metaDirs: string[]
) => {
  const funcArrs = metaDirs.map((item) => {
    const metaConf = metaContent[item];
    if (!metaConf) {
      return Promise.resolve();
    }

    return metaConf.listFunc(workspaceId, {
      workspaceId,
      catalogName,
      schemaName
    });
  });

  const resList = await Promise.all(funcArrs);
  const hasAdd = await getSchemaDetail(workspaceId, `${catalogName}.${schemaName}`, metaDirs);
  const lastResult = [];
  resList.forEach((res: any) => {
    const resResult = res && res.result;
    if (!resResult) {
      return;
    }
    const {volumes, datasets, models, tables} = resResult;
    const itemRes = volumes || datasets || models || tables || [];
    lastResult.push(...itemRes);
  });
  return {list: lastResult, hasAdd};
};

export const getVolumeFolderList = async (
  workspaceId: string,
  catalogName: string,
  schemaName: string,
  volumeName: string,
  versionName: string,
  metaDirs: string[],
  path: string
) => {
  const funcArrs = metaDirs.map((item) => {
    const metaConf = metaContent[item];
    if (!metaConf) {
      return Promise.resolve();
    }
    return metaConf.folderListFunc(workspaceId, {
      catalogName,
      schemaName,
      volumeName,
      versionName,
      path
    });
  });

  const fullName = `${catalogName}.${schemaName}.${volumeName}`;

  const metaDirMapDetailFunc = {
    volume: getVolumeDetail,
    dataset: getDatasetDetail,
    table: getTableDetail,
    model: getModelDetail
  };

  const hasAddList = await Promise.all(
    metaDirs.map((item) => {
      const func = metaDirMapDetailFunc[item];
      return func(workspaceId, fullName);
    })
  );

  const resList = await Promise.all(funcArrs);
  const lastResult = [];
  resList.forEach((res: any) => {
    if (!res) {
      return;
    }
    const itemRes = res.success ? res.result.files.filter((item) => !item.size).map((item) => item.name) : [];
    lastResult.push(...itemRes);
  });
  return {list: lastResult, hasAdd: hasAddList.some((item) => item)};
};

export const getSearchList = async (workspaceId, searchValue) => {
  // TODO 目前接口不支持 数据集 和 模型。联调需要关注。
  const res = await http.searchMeta(workspaceId, searchValue, [
    EntityType.CATALOG,
    EntityType.SCHEMA,
    EntityType.VOLUME
  ]);
  return res.result.map((item) => ({
    value: item.name,
    path: item.parentName ? [...item.parentName.split('.'), item.name] : [item.name]
  }));
};

export const createSchemaNameRules = (workspaceId, catalog) => {
  const ruleInfo = {
    rule: RULE.specialNameStartEn64,
    text: RULE.specialNameStartEn64Text
  };
  console.log('90 :>> ', 90);
  return [
    {
      validator: async (_, value) => {
        console.log('91 :>> ', ruleInfo.rule);
        console.log('911 :>> ', value);
        // 校验特殊字符和长度限制
        if (!ruleInfo.rule.test(value)) {
          console.log('91 :>> ', ruleInfo.text);
          return Promise.reject(new Error(ruleInfo.text));
        }
        // 异步校验Volume名称是否重复，复用查询接口 silent模式
        const res = await http.getSchemaDetail(workspaceId, `${catalog}.${value}`, true);
        if (res.success && res.result?.schema?.id) {
          return Promise.reject(new Error(`该${MetaCnNameMap['Schema']}名称已存在，请重新输入`));
        }
        return Promise.resolve();
      }
    }
  ];
};

export const getLevel = (pathLength) => Math.min(PathLevel.Volume, pathLength);

const getCatalogDetail = async (workspaceId, catalog) => {
  const res = await http.getCatalogDetail(workspaceId, catalog);
  return res.result.catalog.privileges.includes(Privilege.CreateSchema);
};

const getSchemaDetail = async (workspaceId, fullName, metaDirs) => {
  const privilegeMap = {
    volume: Privilege.CreateVolume,
    dataset: Privilege.CreateDataset,
    model: Privilege.CreateModel
  };
  const res = await http.getSchemaDetail(workspaceId, fullName);
  return metaDirs
    .map((item) => privilegeMap[item])
    .some((item) => res.result.schema.privileges.includes(item));
};

const getVolumeDetail = async (workspaceId, catalog) => {
  const res = await http.getVolumeDetail(workspaceId, catalog);
  return res.result.privileges;
};

const getTableDetail = async (workspaceId, catalog) => {
  const res = await http.getTableDetail(workspaceId, catalog);
  return res.result.privileges;
};

const getModelDetail = async (workspaceId, catalog) => {
  const res = await http.getModelDetail(workspaceId, catalog);
  return res.result.privileges;
};

const getDatasetDetail = async (workspaceId, catalog) => {
  const res = await http.getDatasetDetail(workspaceId, catalog);
  return res.result.privileges;
};
