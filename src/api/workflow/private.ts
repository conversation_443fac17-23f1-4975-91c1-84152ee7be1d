import {Privilege} from '@api/permission/type';
import {BaseResponseType, request, urlPrefix} from '../apiFunction';
import {detailJob} from '@api/job';

export enum WorkspaceType {
  MULTIMODAL = 'MULTIMODAL',
  STRUCTURED = 'STRUCTURED'
}
export interface IWorkspaceObj {
  workspaceId?: string;
  workspaceType?: WorkspaceType;
  name?: string;
}
export interface IJobObj {
  jobId?: string;
  workspaceId?: string;
  name?: string;
}
/** 私有化 工作空间列表 */
export function workspacesListPrivate(): BaseResponseType<{items: IWorkspaceObj[]}> {
  return request({
    url: `${urlPrefix}/workspaces/simplelist`,
    method: 'GET'
  }).then((res: any) => {
    return {
      ...res,
      result: {
        ...res.result,
        items: (res.result.items as IWorkspaceObj[]).map((item) => ({
          ...item,
          // 手动添加权限
          privileges: [Privilege.WorkflowMenu]
        }))
      }
    };
  });
}
/** 私有化 空间详情 */
export function workspacesDetailPrivate(
  workspaceId: string,
  workspaceType: WorkspaceType
): BaseResponseType<IWorkspaceObj> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/detail`,
    method: 'GET',
    params: {
      workspaceType
    }
  });
}
/** 私有化 工作流列表 */
export function jobListPrivate(
  workspaceId: string,
  workspaceType: WorkspaceType
): BaseResponseType<{jobs: IJobObj[]}> {
  return request({
    url: `${urlPrefix}/workspaces/${workspaceId}/jobs/simplelist`,
    method: 'POST',
    data: {
      workspaceType,
      pageNo: 1,
      pageSize: 100000
    }
  });
}

/** 私有化 工作流详情 */
export function jobDetailPrivate(
  workspaceId: string,
  workspaceType: WorkspaceType,
  jobId: string
): BaseResponseType<IJobObj> {
  if (workspaceType === WorkspaceType.STRUCTURED) {
    return new Promise((resolve, reject) => {
      resolve({
        status: 200,
        success: true,
        result: {
          name: jobId
        }
      });
    });
  }
  return detailJob(workspaceId, jobId);
}
