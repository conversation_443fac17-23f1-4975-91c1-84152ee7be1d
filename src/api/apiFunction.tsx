/**
 * 针对产品整体进行 API 封装
 * - 公有云环境： 直接使用 @baidu/bce-react-toolkit 中的 request
 * - 私有化环境： 使用 axios.create 创建的 request
 *
 * <AUTHOR>
 */

import Cookies from 'js-cookie';
import {toast} from 'acud';
import flags from '@/flags';
import {
  request as bcetoolkitRequest,
  GlobalNotifyDescription,
  GlobalNotifyMessage
} from '@baidu/bce-react-toolkit';

import axios, {AxiosInstance, AxiosResponse, InternalAxiosRequestConfig} from 'axios';

declare module 'axios' {
  interface AxiosRequestConfig {
    /** 开启静默请求 */
    silent?: boolean;
    CSRFToken?: boolean;
    'x-silent'?: boolean;
    'X-Silence'?: boolean;
    'x-silent-codes'?: Array<string>;
    resContainPage?: boolean;
    raw?: boolean;
  }
}

export const RESPONSE_CODE = {
  SUCCESS: 1,
  ERROR: -1,
  REDIRECT: 20001
};

const isPrivate = flags.DatabuilderPrivateSwitch;

toast.config({
  top: 60
});

let request: AxiosInstance;
if (isPrivate) {
  // 私有云使用 axios 创建新的实例
  request = axios.create({
    baseURL: '',
    timeout: 0,
    CSRFToken: true
  });
  // 请求拦截器
  request.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      // 添加通用query参数
      // const locale = window?.$framework?.i18n?.getCurrentLanguage();
      if (config.url) {
        config.url += config?.url?.indexOf('?') > 0 ? '&' : '?';

        // url中没有locale参数，增加locale参数
        // if (!config.url.match(/locale=/)) {
        //   config.url += 'locale=' + locale + '&';
        // }

        config.url += '_=' + new Date().getTime();
      }

      const csrftoken = Cookies.get('bce-user-info'); // 公有云和私有云都使用 bce-user-info cookie 存储 CSRF Token
      config.headers['csrftoken'] = csrftoken || new Date().getTime();

      // 支持关闭 CSRF 校验
      // if (config.CSRFToken !== false && !csrftoken && !window.IGNORE_CSRFTOKEN_VERIFY) {
      //   // 适配虚商鉴权失败跳转
      //   const logoutUrl: string = window?.$framework?.frameworkStore?.constants?.logoutUrl || '';
      //   const url = logoutUrl.substring(0, logoutUrl.length - 6);

      //   if (window.ISXSCONSOLE && /login.bce(test)?.baidu.com/.test(logoutUrl)) {
      //     window.parent.postMessage(
      //       {
      //         code: 'authentication failed'
      //       },
      //       window.XSREFERRER as string
      //     );
      //   } else {
      //     location.href = url + '?redirect=' + location.href;
      //   }
      //   return config;
      // }

      // 是否指定服务环境
      // const version = sessionStorage.getItem('X-Bce-Access-Version');
      // if (version) {
      //   config.headers['X-Bce-Access-Version'] = version;
      // }

      return config;
    },
    (err) => Promise.reject(err)
  );

  request.interceptors.response.use(
    async (response: AxiosResponse) => {
      const {status, data, headers, config} = response;
      const {code, message, cancelled} = response.data;
      const csrftoken = config.headers.csrftoken;
      const responseData = config.raw ? response : data;

      // firefox中请求未完成时刷新页面，仍然执行notifications问题
      if (status === 0 && response.data === '' && response.statusText === '') {
        return;
      }

      // 重定向：暂时直接跳转到登录页，FIXME: 后续优化实现弹窗提示登录
      const redirectUrl = responseData?.data?.redirect;
      if ((code === RESPONSE_CODE.REDIRECT || message) && redirectUrl) {
        location.href = redirectUrl;
        return responseData;
      }

      //  静默模式直接返回不处理
      if (
        config['x-silent'] ||
        config['X-Silence'] ||
        config.silent ||
        (config['x-silent-codes'] && config['x-silent-codes'].indexOf(code) !== -1)
      ) {
        return responseData;
      }

      // 报错弹窗处理
      if (status === 200) {
        if ([false, 'false'].includes(data?.success)) {
          if (message?.global) {
            const requestId = data?.requestId;
            const ticket = '';
            const module = config?.url?.split('/')[2].toUpperCase() || '';
            toast.error({
              message: (
                <GlobalNotifyMessage
                  requestId={requestId!}
                  ticket={ticket}
                  module={module}
                  message={data.message.global}
                ></GlobalNotifyMessage>
              ),
              description: <GlobalNotifyDescription requestId={requestId}></GlobalNotifyDescription>,
              key: requestId,
              className: 'global-toast-error-container'
            });
          } else if (message && message.noSession) {
            toast.error({
              message: <GlobalNotifyMessage message={message.noSession}></GlobalNotifyMessage>,
              description: '',
              key: data?.requestId,
              className: 'global-toast-error-container'
            });
          } else if (message && !message.field) {
            toast.error({
              message: <GlobalNotifyMessage message={'系统提示：请求失败(未知错误)'}></GlobalNotifyMessage>,
              description: '',
              key: data?.requestId,
              className: 'global-toast-error-container'
            });
          }
          //  统一处理success false为500
          response.status = 500;
        }
      } else {
        toast.error({
          message: <GlobalNotifyMessage message={`服务器错误：${response.status}`}></GlobalNotifyMessage>,
          description: `请求路径：${config.url}`,
          key: data?.requestId,
          className: 'global-toast-error-container'
        });
      }

      return responseData;
    },
    (err) => Promise.reject(err)
  );
} else {
  // 公有云直接使用 @baidu/bce-react-toolkit 的 request
  request = bcetoolkitRequest;
}

export type BaseResponseType<T> = Promise<{
  success: boolean;
  status: number;
  result: T;
}>;

export const urlPrefix = isPrivate ? '/databuilder/api/databuilder/v1' : '/api/databuilder/v1';

export {request};
